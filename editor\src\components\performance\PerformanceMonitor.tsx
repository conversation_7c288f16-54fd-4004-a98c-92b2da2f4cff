/**
 * 性能监控组件
 * 
 * 提供实时性能监控界面，包括：
 * - 引擎性能指标显示
 * - 内存使用监控
 * - 缓存命中率统计
 * - 性能优化建议
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Switch,
  Button,
  Alert,
  Tabs,
  Space,
  Tag,
  Tooltip,
  Modal,
  Form,
  InputNumber
} from 'antd';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar
} from 'recharts';
import {
  ThunderboltOutlined,
  DatabaseOutlined,
  DashboardOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { OptimizedBehaviorTreeEngine, OptimizedPerceptionSystem } from '../../libs/dl-engine-types';

const { TabPane } = Tabs;

/**
 * 性能数据接口
 */
interface PerformanceData {
  timestamp: number;
  executionTime: number;
  memoryUsage: number;
  cacheHitRate: number;
  parallelEfficiency: number;
  nodeExecutionCount: number;
  averageNodeTime: number;
}

/**
 * 优化建议接口
 */
interface OptimizationSuggestion {
  type: 'warning' | 'info' | 'success';
  title: string;
  description: string;
  action?: string;
  priority: number;
}

/**
 * 性能监控组件
 */
const PerformanceMonitor: React.FC<{
  engineInstance?: OptimizedBehaviorTreeEngine;
  perceptionInstance?: OptimizedPerceptionSystem;
  onConfigChange?: (config: any) => void;
}> = ({ engineInstance, perceptionInstance, onConfigChange }) => {
  // 状态管理
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<any>({});
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [optimizationConfig, setOptimizationConfig] = useState({
    enableObjectPooling: true,
    enableParallelExecution: true,
    enableCaching: true,
    enableSIMD: true,
    maxPoolSize: 1000,
    cacheSize: 10000,
    parallelThreshold: 5
  });

  // 引用
  const intervalRef = useRef<NodeJS.Timeout>();
  const chartDataRef = useRef<PerformanceData[]>([]);

  /**
   * 组件初始化
   */
  useEffect(() => {
    if (isMonitoring) {
      startMonitoring();
    } else {
      stopMonitoring();
    }

    return () => {
      stopMonitoring();
    };
  }, [isMonitoring, engineInstance, perceptionInstance]);

  /**
   * 开始监控
   */
  const startMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      collectPerformanceData();
    }, 1000); // 每秒更新一次
  };

  /**
   * 停止监控
   */
  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = undefined;
    }
  };

  /**
   * 收集性能数据
   */
  const collectPerformanceData = () => {
    try {
      let engineMetrics = {};
      let perceptionMetrics = {};

      // 收集引擎性能数据
      if (engineInstance) {
        engineMetrics = engineInstance.getPerformanceMetrics();
      }

      // 收集感知系统性能数据
      if (perceptionInstance) {
        perceptionMetrics = perceptionInstance.getPerformanceStats();
      }

      // 合并指标
      const combinedMetrics: any = {
        ...engineMetrics,
        ...perceptionMetrics,
        timestamp: Date.now()
      };

      setCurrentMetrics(combinedMetrics);

      // 添加到历史数据
      const newDataPoint: PerformanceData = {
        timestamp: Date.now(),
        executionTime: combinedMetrics.executionTime || 0,
        memoryUsage: combinedMetrics.memoryUsage || 0,
        cacheHitRate: combinedMetrics.cacheHitRate || 0,
        parallelEfficiency: combinedMetrics.parallelEfficiency || 0,
        nodeExecutionCount: combinedMetrics.nodeExecutionCount || 0,
        averageNodeTime: combinedMetrics.averageNodeTime || 0
      };

      chartDataRef.current.push(newDataPoint);

      // 限制数据点数量
      if (chartDataRef.current.length > 60) {
        chartDataRef.current.shift();
      }

      setPerformanceData([...chartDataRef.current]);

      // 生成优化建议
      generateOptimizationSuggestions(combinedMetrics);

    } catch (error) {
      console.error('性能数据收集失败:', error);
    }
  };

  /**
   * 生成优化建议
   */
  const generateOptimizationSuggestions = (metrics: any) => {
    const newSuggestions: OptimizationSuggestion[] = [];

    // 内存使用建议
    if (metrics.memoryUsage > 500) {
      newSuggestions.push({
        type: 'warning',
        title: '内存使用过高',
        description: `当前内存使用 ${metrics.memoryUsage}MB，建议启用垃圾回收优化`,
        action: '优化内存',
        priority: 3
      });
    }

    // 缓存命中率建议
    if (metrics.cacheHitRate < 0.7) {
      newSuggestions.push({
        type: 'info',
        title: '缓存命中率偏低',
        description: `当前缓存命中率 ${(metrics.cacheHitRate * 100).toFixed(1)}%，建议增加缓存大小`,
        action: '调整缓存',
        priority: 2
      });
    }

    // 并行效率建议
    if (metrics.parallelEfficiency < 0.6) {
      newSuggestions.push({
        type: 'warning',
        title: '并行效率不佳',
        description: `并行效率 ${(metrics.parallelEfficiency * 100).toFixed(1)}%，建议调整并行阈值`,
        action: '优化并行',
        priority: 3
      });
    }

    // 执行时间建议
    if (metrics.averageNodeTime > 10) {
      newSuggestions.push({
        type: 'warning',
        title: '节点执行时间过长',
        description: `平均节点执行时间 ${metrics.averageNodeTime.toFixed(2)}ms，建议优化算法`,
        action: '算法优化',
        priority: 4
      });
    }

    // 性能良好提示
    if (newSuggestions.length === 0) {
      newSuggestions.push({
        type: 'success',
        title: '性能状态良好',
        description: '所有性能指标都在正常范围内',
        priority: 1
      });
    }

    setSuggestions(newSuggestions);
  };

  /**
   * 应用优化配置
   */
  const applyOptimizationConfig = () => {
    try {
      if (engineInstance) {
        // 应用引擎优化配置
        engineInstance.tunePerformance();
      }

      if (perceptionInstance) {
        // 应用感知系统优化配置
        perceptionInstance.optimizeParameters();
      }

      if (onConfigChange) {
        onConfigChange(optimizationConfig);
      }

      setShowConfigModal(false);
      
      Modal.success({
        title: '配置应用成功',
        content: '性能优化配置已成功应用'
      });

    } catch (error) {
      Modal.error({
        title: '配置应用失败',
        content: error.message
      });
    }
  };

  /**
   * 重置性能统计
   */
  const resetStats = () => {
    try {
      if (engineInstance) {
        engineInstance.resetPerformanceStats();
      }

      if (perceptionInstance) {
        perceptionInstance.resetPerformanceStats();
      }

      chartDataRef.current = [];
      setPerformanceData([]);
      setCurrentMetrics({});

      Modal.success({
        title: '统计重置成功',
        content: '性能统计数据已重置'
      });

    } catch (error) {
      Modal.error({
        title: '重置失败',
        content: error.message
      });
    }
  };

  /**
   * 渲染实时指标卡片
   */
  const renderMetricsCards = () => (
    <Row gutter={[16, 16]}>
      <Col span={6}>
        <Card>
          <Statistic
            title="执行时间"
            value={currentMetrics.executionTime || 0}
            suffix="ms"
            prefix={<ThunderboltOutlined />}
            valueStyle={{ color: currentMetrics.executionTime > 50 ? '#cf1322' : '#3f8600' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="内存使用"
            value={currentMetrics.memoryUsage || 0}
            suffix="MB"
            prefix={<DatabaseOutlined />}
            valueStyle={{ color: currentMetrics.memoryUsage > 500 ? '#cf1322' : '#3f8600' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="缓存命中率"
            value={(currentMetrics.cacheHitRate * 100) || 0}
            suffix="%"
            prefix={<DashboardOutlined />}
            precision={1}
            valueStyle={{ color: currentMetrics.cacheHitRate < 0.7 ? '#cf1322' : '#3f8600' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="并行效率"
            value={(currentMetrics.parallelEfficiency * 100) || 0}
            suffix="%"
            prefix={<ThunderboltOutlined />}
            precision={1}
            valueStyle={{ color: currentMetrics.parallelEfficiency < 0.6 ? '#cf1322' : '#3f8600' }}
          />
        </Card>
      </Col>
    </Row>
  );

  /**
   * 渲染性能图表
   */
  const renderPerformanceCharts = () => (
    <Row gutter={[16, 16]}>
      <Col span={12}>
        <Card title="执行时间趋势" size="small">
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={(value) => new Date(value).toLocaleTimeString()}
              />
              <YAxis />
              <RechartsTooltip 
                labelFormatter={(value) => new Date(value).toLocaleTimeString()}
                formatter={(value: number) => [`${value.toFixed(2)}ms`, '执行时间']}
              />
              <Line 
                type="monotone" 
                dataKey="executionTime" 
                stroke="#1890ff" 
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </Card>
      </Col>
      <Col span={12}>
        <Card title="内存使用趋势" size="small">
          <ResponsiveContainer width="100%" height={200}>
            <AreaChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={(value) => new Date(value).toLocaleTimeString()}
              />
              <YAxis />
              <RechartsTooltip 
                labelFormatter={(value) => new Date(value).toLocaleTimeString()}
                formatter={(value: number) => [`${value.toFixed(2)}MB`, '内存使用']}
              />
              <Area 
                type="monotone" 
                dataKey="memoryUsage" 
                stroke="#52c41a" 
                fill="#52c41a"
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>
      </Col>
      <Col span={12}>
        <Card title="缓存命中率" size="small">
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={(value) => new Date(value).toLocaleTimeString()}
              />
              <YAxis domain={[0, 1]} tickFormatter={(value) => `${(value * 100).toFixed(0)}%`} />
              <RechartsTooltip 
                labelFormatter={(value) => new Date(value).toLocaleTimeString()}
                formatter={(value: number) => [`${(value * 100).toFixed(1)}%`, '缓存命中率']}
              />
              <Line 
                type="monotone" 
                dataKey="cacheHitRate" 
                stroke="#722ed1" 
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </Card>
      </Col>
      <Col span={12}>
        <Card title="节点执行统计" size="small">
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={performanceData.slice(-10)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={(value) => new Date(value).toLocaleTimeString().slice(-8, -3)}
              />
              <YAxis />
              <RechartsTooltip 
                labelFormatter={(value) => new Date(value).toLocaleTimeString()}
                formatter={(value: number) => [value, '执行次数']}
              />
              <Bar dataKey="nodeExecutionCount" fill="#fa8c16" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </Col>
    </Row>
  );

  /**
   * 渲染优化建议
   */
  const renderOptimizationSuggestions = () => (
    <Card title="优化建议" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        {suggestions.map((suggestion, index) => (
          <Alert
            key={index}
            type={suggestion.type}
            message={suggestion.title}
            description={suggestion.description}
            showIcon
            action={
              suggestion.action && (
                <Button size="small" type="link">
                  {suggestion.action}
                </Button>
              )
            }
          />
        ))}
      </Space>
    </Card>
  );

  /**
   * 渲染配置模态框
   */
  const renderConfigModal = () => (
    <Modal
      title="性能优化配置"
      open={showConfigModal}
      onOk={applyOptimizationConfig}
      onCancel={() => setShowConfigModal(false)}
      width={600}
    >
      <Form layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="启用对象池">
              <Switch
                checked={optimizationConfig.enableObjectPooling}
                onChange={(checked) => 
                  setOptimizationConfig(prev => ({ ...prev, enableObjectPooling: checked }))
                }
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="启用并行执行">
              <Switch
                checked={optimizationConfig.enableParallelExecution}
                onChange={(checked) => 
                  setOptimizationConfig(prev => ({ ...prev, enableParallelExecution: checked }))
                }
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="启用缓存">
              <Switch
                checked={optimizationConfig.enableCaching}
                onChange={(checked) => 
                  setOptimizationConfig(prev => ({ ...prev, enableCaching: checked }))
                }
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="启用SIMD优化">
              <Switch
                checked={optimizationConfig.enableSIMD}
                onChange={(checked) => 
                  setOptimizationConfig(prev => ({ ...prev, enableSIMD: checked }))
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="对象池大小">
              <InputNumber
                min={100}
                max={10000}
                value={optimizationConfig.maxPoolSize}
                onChange={(value) => 
                  setOptimizationConfig(prev => ({ ...prev, maxPoolSize: value || 1000 }))
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="缓存大小">
              <InputNumber
                min={1000}
                max={50000}
                value={optimizationConfig.cacheSize}
                onChange={(value) => 
                  setOptimizationConfig(prev => ({ ...prev, cacheSize: value || 10000 }))
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="并行阈值">
              <InputNumber
                min={1}
                max={20}
                value={optimizationConfig.parallelThreshold}
                onChange={(value) => 
                  setOptimizationConfig(prev => ({ ...prev, parallelThreshold: value || 5 }))
                }
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );

  return (
    <div style={{ padding: '16px' }}>
      {/* 控制栏 */}
      <Card size="small" style={{ marginBottom: '16px' }}>
        <Space>
          <span>性能监控:</span>
          <Switch
            checked={isMonitoring}
            onChange={setIsMonitoring}
            checkedChildren="开启"
            unCheckedChildren="关闭"
          />
          <Button 
            icon={<SettingOutlined />} 
            onClick={() => setShowConfigModal(true)}
          >
            优化配置
          </Button>
          <Button onClick={resetStats}>
            重置统计
          </Button>
          <Tag color={isMonitoring ? 'green' : 'red'}>
            {isMonitoring ? '监控中' : '已停止'}
          </Tag>
        </Space>
      </Card>

      {/* 主要内容 */}
      <Tabs defaultActiveKey="metrics">
        <TabPane tab="实时指标" key="metrics">
          <Space direction="vertical" style={{ width: '100%' }}>
            {renderMetricsCards()}
            {renderOptimizationSuggestions()}
          </Space>
        </TabPane>
        
        <TabPane tab="性能图表" key="charts">
          {renderPerformanceCharts()}
        </TabPane>
        
        <TabPane tab="详细报告" key="report">
          <Card title="性能详细报告">
            <pre style={{ background: '#f5f5f5', padding: '16px', borderRadius: '4px' }}>
              {JSON.stringify(currentMetrics, null, 2)}
            </pre>
          </Card>
        </TabPane>
      </Tabs>

      {/* 配置模态框 */}
      {renderConfigModal()}
    </div>
  );
};

export default PerformanceMonitor;
